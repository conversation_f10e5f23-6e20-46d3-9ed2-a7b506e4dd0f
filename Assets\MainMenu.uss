/* Unity USS Styles for Main Menu */

/* Root container styles */
.unity-ui-document__root {
    background-color: rgb(18, 5, 36);
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Background container */
.background-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

/* Side lights effect */
.side-lights {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(77, 238, 234, 0.1);
    border-radius: 50%;
    opacity: 0.7;
}

/* Corner lights effect */
.corner-lights {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background-color: rgba(255, 231, 0, 0.2);
    border-radius: 50%;
    opacity: 0.7;
}

/* Cosmic background */
.cosmic-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(25, 0, 50, 0.8);
    opacity: 0.6;
}

/* Stars effect */
.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0.8;
}

/* Chess piece decoration */
.chess-piece {
    position: absolute;
    width: 60px;
    height: 60px;
    right: 10%;
    top: 20%;
    opacity: 0.6;
}

.chess-icon {
    font-size: 60px;
    color: rgb(77, 238, 234);
    -unity-text-align: middle-center;
}

/* Menu container */
.menu-container {
    position: relative;
    background-color: rgba(10, 10, 32, 0.9);
    padding: 48px 32px;
    border-radius: 20px;
    border-width: 2px;
    border-color: rgba(0, 255, 255, 0.2);
    max-width: 90%;
    width: 100%;
    margin: 16px;
    overflow: hidden;
    justify-content: center;
    align-items: center;
}

/* Menu layout */
.menu {
    flex-direction: column;
    margin-top: 48px;
    width: 100%;
}

/* Game title */
.game-title {
    font-size: 72px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    margin-bottom: 16px;
    position: relative;
    letter-spacing: 4px;
    flex-direction: column;
}

.title-line {
    font-size: 72px;
    color: rgb(77, 238, 234);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    text-shadow: 0 0 15px rgba(77, 238, 234, 0.6);
    margin-bottom: 8px;
}

/* Subtitle */
.subtitle {
    font-size: 40px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    margin-bottom: 32px;
    position: relative;
    letter-spacing: 6px;
    flex-direction: column;
}

.subtitle-text {
    font-size: 40px;
    color: rgb(255, 107, 107);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

/* Menu buttons */
.menu-button {
    width: 100%;
    padding: 28px;
    font-size: 28px;
    -unity-font-style: bold;
    letter-spacing: 3px;
    border-radius: 25px;
    background-color: rgba(77, 238, 234, 0.3);
    color: rgb(255, 255, 255);
    border-width: 3px;
    border-color: rgba(77, 238, 234, 0.4);
    text-shadow: 0 0 8px rgba(77, 238, 234, 0.7);
    margin-bottom: 12px;
    transition-duration: 0.3s;
    -unity-text-align: middle-center;
}

/* Button hover effects */
.menu-button:hover {
    background-color: rgba(77, 238, 234, 0.5);
    border-color: rgba(77, 238, 234, 0.6);
    scale: 1.02;
}

.menu-button:active {
    scale: 0.98;
}

/* Message box */
.message-box {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-color: rgba(77, 238, 234, 0.1);
    padding: 32px;
    border-radius: 15px;
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.3);
    opacity: 0;
    visibility: hidden;
    transition-duration: 0.3s;
}

.message-box.active {
    opacity: 1;
    visibility: visible;
}

.message-content {
    position: relative;
    padding: 16px;
    align-items: center;
    justify-content: center;
}

.cyber-border {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-width: 2px;
    border-color: rgb(77, 238, 234);
    border-radius: 12px;
}

.welcome-title {
    font-size: 35px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 2px;
    margin-bottom: 16px;
    color: rgb(77, 238, 234);
    text-shadow: 0 0 20px rgba(77, 238, 234, 0.5);
}

.separator {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 24px 0;
}

.separator-line {
    height: 2px;
    width: 50px;
    background-color: rgb(77, 238, 234);
}

.diamond {
    width: 12px;
    height: 12px;
    background-color: rgb(77, 238, 234);
    margin: 0 10px;
    rotate: 45deg;
}

.welcome-text {
    font-size: 28px;
    -unity-font-style: bold;
    color: rgb(255, 255, 255);
    text-shadow: 0 0 10px rgba(77, 238, 234, 0.7);
    margin-bottom: 24px;
    -unity-text-align: middle-center;
    letter-spacing: 1px;
}

.pulse-icon {
    font-size: 40px;
    margin-top: 24px;
    color: rgb(240, 0, 255);
    text-shadow: 0 0 15px rgb(240, 0, 255);
    -unity-text-align: middle-center;
}

/* Welcome box */
.welcome-box {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    scale: 0.9;
    background-color: rgba(77, 238, 234, 0.1);
    padding: 32px;
    border-radius: 15px;
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.3);
    -unity-text-align: middle-center;
    opacity: 0;
    visibility: hidden;
    transition-duration: 0.3s;
}

.welcome-box.active {
    opacity: 1;
    visibility: visible;
    scale: 1;
}

.welcome-content {
    align-items: center;
    justify-content: center;
}

.welcome-subtitle {
    color: rgb(77, 238, 234);
    font-size: 24px;
    margin-bottom: 8px;
    -unity-text-align: middle-center;
}

.welcome-main-title {
    color: rgb(240, 0, 255);
    font-size: 32px;
    margin-bottom: 24px;
    -unity-text-align: middle-center;
    -unity-font-style: bold;
}

.start-button {
    background-color: rgb(77, 238, 234);
    border: none;
    padding: 16px 32px;
    color: white;
    font-size: 19px;
    border-radius: 8px;
    transition-duration: 0.1s;
    -unity-text-align: middle-center;
}

.start-button:hover {
    scale: 1.05;
}

/* Sound button */
.sound-button.top-right {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    padding: 0;
    font-size: 24px;
    background-color: rgba(77, 238, 234, 0.2);
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.4);
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    transition-duration: 0.3s;
}

.sound-button.top-right:hover {
    scale: 1.1;
    background-color: rgba(77, 238, 234, 0.4);
}

.sound-button.top-right.muted {
    background-color: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.4);
}

.sound-icon {
    font-size: 24px;
    color: rgb(77, 238, 234);
    -unity-text-align: middle-center;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}

