/* Unity USS Styles for Main Menu */

/* Root container styles */
.unity-ui-document__root {
    background-image: linear-gradient(135deg, rgb(20, 10, 40) 0%, rgb(40, 20, 60) 50%, rgb(60, 30, 80) 100%);
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Menu container - the main card */
.menu-container {
    background-color: rgba(30, 50, 80, 0.8);
    padding: 60px 40px;
    border-radius: 20px;
    border-width: 2px;
    border-color: rgb(0, 200, 255);
    max-width: 400px;
    width: 90%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

/* Game title container */
.game-title {
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

/* Title lines */
.title-mate {
    font-size: 48px;
    color: rgb(255, 0, 255);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 4px;
    margin-bottom: 5px;
}

.title-the-king {
    font-size: 48px;
    color: rgb(255, 0, 255);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 4px;
    margin-bottom: 20px;
}

/* Subtitle */
.subtitle-text {
    font-size: 24px;
    color: rgb(255, 150, 100);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 3px;
    margin-bottom: 40px;
}

/* Play button */
.play-button {
    width: 100%;
    padding: 20px;
    font-size: 24px;
    -unity-font-style: bold;
    letter-spacing: 2px;
    border-radius: 15px;
    background-image: linear-gradient(135deg, rgb(100, 50, 150) 0%, rgb(150, 50, 200) 100%);
    color: rgb(255, 255, 255);
    border-width: 2px;
    border-color: rgb(0, 200, 255);
    -unity-text-align: middle-center;
    transition-duration: 0.3s;
}

/* Button hover effects */
.play-button:hover {
    background-image: linear-gradient(135deg, rgb(120, 70, 170) 0%, rgb(170, 70, 220) 100%);
    border-color: rgb(50, 220, 255);
    scale: 1.05;
}

.play-button:active {
    scale: 0.95;
}
