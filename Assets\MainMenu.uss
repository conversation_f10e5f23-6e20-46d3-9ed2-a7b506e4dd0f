/* Unity USS Styles for Main Menu - Converted from your amazing CSS design! */

/* Root container with cosmic background */
.unity-ui-document__root {
    background-image: url('project://database/Assets/assets/image/menu-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Enhanced play button matching the image design */
.play-button {
    width: 200px;
    height: 60px;
    border-radius: 30px;
    background-image: linear-gradient(135deg, #ffd6e8, #fff0f6);
    color: #ff7fa8;
    font-size: 24px;
    font-family: 'Comic Neue', sans-serif;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(255, 180, 210, 0.7);
    unity-text-align: middle-center;
    transition: all 0.4s ease;
    box-shadow: 0 0 15px rgba(255, 180, 200, 0.4);
}

/* Button hover effects */
.play-button:hover {
    background-image: linear-gradient(135deg, #ffe1ed, #fff6fa);
    box-shadow: 0 0 25px rgba(255, 160, 200, 0.8);
    color: #ff97b6;
}

.play-button:active {
    translate: 0 1px;
    scale: 0.98;
}

/* "Breathing" animation */
@keyframes softGlow {
    0% { box-shadow: 0 0 15px rgba(255, 160, 190, 0.4); }
    50% { box-shadow: 0 0 30px rgba(255, 140, 180, 0.8); }
    100% { box-shadow: 0 0 15px rgba(255, 160, 190, 0.4); }
}

.play-button {
    animation-name: softGlow;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}

