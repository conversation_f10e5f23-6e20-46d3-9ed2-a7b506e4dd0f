/* Unity USS Styles for Main Menu - Converted from your amazing CSS design! */

/* Root container with cosmic background */
.unity-ui-document__root {
    background-image: url('project://database/Assets/assets/image/menu-background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Background effects layers */
.background-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

/* Side lights effect */
.side-lights {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(77, 238, 234, 0.2);
    opacity: 0.7;
}

/* Corner lights effect */
.corner-lights {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background-color: rgba(255, 231, 0, 0.2);
    border-radius: 50%;
    opacity: 0.7;
}

/* Cosmic background */
.cosmic-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(25, 0, 50, 0.8);
    opacity: 0.6;
}

/* Stars effect */
.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0.8;
}

/* Chess piece decoration */
.chess-piece {
    position: absolute;
    width: 60px;
    height: 60px;
    right: 10%;
    top: 20%;
    opacity: 0.6;
}

.chess-icon {
    font-size: 60px;
    color: rgb(77, 238, 234);
    -unity-text-align: middle-center;
}

/* Enhanced menu container matching the image design */
.menu-container {
    position: relative;
    background-image: linear-gradient(135deg, rgba(15, 25, 45, 0.85) 0%, rgba(25, 15, 45, 0.85) 50%, rgba(35, 25, 55, 0.85) 100%);
    padding: 60px 40px;
    border-radius: 25px;
    border-width: 3px;
    border-color: rgba(0, 255, 255, 0.6);
    max-width: 450px;
    width: 85%;
    margin: 20px;
    overflow: hidden;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 500px;
}

/* Game title container */
.game-title {
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    position: relative;
    letter-spacing: 8px;
}

/* Title lines with bright magenta like in the image */
.title-mate {
    font-size: 64px;
    color: rgb(255, 0, 255);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 8px;
    margin-bottom: 5px;
    text-shadow: 0 0 20px rgba(255, 0, 255, 0.8);
}

.title-the-king {
    font-size: 64px;
    color: rgb(255, 0, 255);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 8px;
    margin-bottom: 30px;
    text-shadow: 0 0 20px rgba(255, 0, 255, 0.8);
}

/* Subtitle with orange color like in the image */
.subtitle-text {
    font-size: 32px;
    color: rgb(255, 165, 100);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 6px;
    margin-bottom: 60px;
    text-shadow: 0 0 15px rgba(255, 165, 100, 0.7);
}

/* Enhanced play button matching the image design */
.play-button {
    width: 90%;
    padding: 25px;
    font-size: 32px;
    -unity-font-style: bold;
    letter-spacing: 4px;
    border-radius: 20px;
    background-image: linear-gradient(135deg, rgba(100, 50, 200, 0.8) 0%, rgba(200, 50, 150, 0.8) 100%);
    color: rgb(255, 255, 255);
    border-width: 3px;
    border-color: rgba(0, 255, 255, 0.7);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    -unity-text-align: middle-center;
    transition-duration: 0.3s;
    overflow: hidden;
    position: relative;
    margin-top: 20px;
}

/* Button hover effects */
.play-button:hover {
    background-image: linear-gradient(135deg, rgba(120, 70, 220, 0.9) 0%, rgba(220, 70, 170, 0.9) 100%);
    border-color: rgba(0, 255, 255, 0.9);
    translate: 0 -3px;
    scale: 1.02;
}

.play-button:active {
    translate: 0 1px;
    scale: 0.98;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}

/* Sound button styling */
.sound-button.top-right {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    padding: 0;
    font-size: 24px;
    background-image: linear-gradient(45deg, rgba(77, 238, 234, 0.2) 0%, rgba(240, 0, 255, 0.2) 100%);
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.4);
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    transition-duration: 0.3s;
}

.sound-button.top-right:hover {
    scale: 1.1;
    background-image: linear-gradient(45deg, rgba(77, 238, 234, 0.4) 0%, rgba(240, 0, 255, 0.4) 100%);
}

.sound-button.top-right.muted {
    background-color: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.4);
}

.sound-icon {
    font-size: 24px;
    color: rgb(77, 238, 234);
    -unity-text-align: middle-center;
}

/* Message box styling */
.message-box {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-image: linear-gradient(45deg, rgba(77, 238, 234, 0.1) 0%, rgba(240, 0, 255, 0.1) 100%);
    padding: 40px;
    border-radius: 15px;
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.3);
    -unity-text-align: middle-center;
    opacity: 0;
    visibility: hidden;
    transition-duration: 0.3s;
}

.message-box.active {
    opacity: 1;
    visibility: visible;
}

.message-content {
    position: relative;
    padding: 16px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.cyber-border {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-width: 2px;
    border-color: rgb(77, 238, 234);
    border-radius: 12px;
}

.welcome-title {
    font-size: 35px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    letter-spacing: 2px;
    margin-bottom: 16px;
    color: rgb(77, 238, 234);
    text-shadow: 0 0 20px rgba(77, 238, 234, 0.5);
}

.separator {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 24px 0;
}

.separator-line {
    height: 2px;
    width: 50px;
    background-color: rgb(77, 238, 234);
}

.diamond {
    width: 12px;
    height: 12px;
    background-color: rgb(77, 238, 234);
    margin: 0 10px;
    rotate: 45deg;
}

.welcome-text {
    font-size: 28px;
    -unity-font-style: bold;
    color: rgb(255, 255, 255);
    text-shadow: 0 0 10px rgba(77, 238, 234, 0.7);
    margin-bottom: 24px;
    -unity-text-align: middle-center;
    letter-spacing: 1px;
}

.pulse-icon {
    font-size: 40px;
    margin-top: 24px;
    color: rgb(240, 0, 255);
    text-shadow: 0 0 15px rgb(240, 0, 255);
    -unity-text-align: middle-center;
}

/* Welcome box styling */
.welcome-box {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    scale: 0.9;
    background-image: linear-gradient(45deg, rgba(77, 238, 234, 0.1) 0%, rgba(240, 0, 255, 0.1) 100%);
    padding: 32px;
    border-radius: 15px;
    border-width: 2px;
    border-color: rgba(77, 238, 234, 0.3);
    -unity-text-align: middle-center;
    opacity: 0;
    visibility: hidden;
    transition-duration: 0.3s;
}

.welcome-box.active {
    opacity: 1;
    visibility: visible;
    scale: 1;
}

.welcome-content {
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.welcome-subtitle {
    color: rgb(77, 238, 234);
    font-size: 24px;
    margin-bottom: 8px;
    -unity-text-align: middle-center;
}

.welcome-main-title {
    color: rgb(240, 0, 255);
    font-size: 32px;
    margin-bottom: 24px;
    -unity-text-align: middle-center;
    -unity-font-style: bold;
}

.start-button {
    background-image: linear-gradient(45deg, rgb(77, 238, 234) 0%, rgb(240, 0, 255) 100%);
    border: none;
    padding: 16px 32px;
    color: white;
    font-size: 19px;
    border-radius: 8px;
    transition-duration: 0.1s;
    -unity-text-align: middle-center;
}

.start-button:hover {
    scale: 1.05;
    translate: 0 -2px;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}
