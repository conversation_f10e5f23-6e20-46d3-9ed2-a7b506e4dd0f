using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.SceneManagement;

public class MainMenuController : MonoBehaviour
{
    private UIDocument menuDocument;
    private Button continueBtn;
    private Button newGameBtn;
    private Button characterBtn;
    private Button galleryBtn;
    private Button settingsBtn;
    private Button saveLoadBtn;
    private Button startButton;
    private Button soundButton;

    private VisualElement messageBox;
    private VisualElement welcomeBox;
    private VisualElement menuContainer;

    private bool isSoundMuted = false;

    private void OnEnable()
    {
        menuDocument = GetComponent<UIDocument>();
        var root = menuDocument.rootVisualElement;

        // Get button references
        startGameBtn = root.Q<Button>("Startgame-btn");

        // Get visual element references
        messageBox = root.Q("message-box");
        welcomeBox = root.Q("welcome-box");
        menuContainer = root.Q("menu-container");

        // Register button callbacks
        if (continueBtn != null) continueBtn.clicked += OnContinueClicked;
        if (newGameBtn != null) newGameBtn.clicked += OnNewGameClicked;
        if (characterBtn != null) characterBtn.clicked += OnCharacterClicked;
        if (galleryBtn != null) galleryBtn.clicked += OnGalleryClicked;
        if (settingsBtn != null) settingsBtn.clicked += OnSettingsClicked;
        if (saveLoadBtn != null) saveLoadBtn.clicked += OnSaveLoadClicked;
        if (startButton != null) startButton.clicked += OnStartButtonClicked;
        if (soundButton != null) soundButton.clicked += OnSoundButtonClicked;

        // Register click events for message boxes
        if (messageBox != null) messageBox.RegisterCallback<ClickEvent>(OnMessageBoxClicked);
        if (welcomeBox != null) welcomeBox.RegisterCallback<ClickEvent>(OnWelcomeBoxClicked);

        // Add fade-in effect
        if (menuContainer != null) menuContainer.AddToClassList("fade-in");

        // Show welcome message after a short delay
        Invoke(nameof(ShowWelcomeMessage), 1f);
    }

    private void OnDisable()
    {
        if (continueBtn != null) continueBtn.clicked -= OnContinueClicked;
        if (newGameBtn != null) newGameBtn.clicked -= OnNewGameClicked;
        if (characterBtn != null) characterBtn.clicked -= OnCharacterClicked;
        if (galleryBtn != null) galleryBtn.clicked -= OnGalleryClicked;
        if (settingsBtn != null) settingsBtn.clicked -= OnSettingsClicked;
        if (saveLoadBtn != null) saveLoadBtn.clicked -= OnSaveLoadClicked;
        if (startButton != null) startButton.clicked -= OnStartButtonClicked;
        if (soundButton != null) soundButton.clicked -= OnSoundButtonClicked;

        if (messageBox != null) messageBox.UnregisterCallback<ClickEvent>(OnMessageBoxClicked);
        if (welcomeBox != null) welcomeBox.UnregisterCallback<ClickEvent>(OnWelcomeBoxClicked);
    }

    private void LoadSceneIfExists(string sceneName)
    {
        // Check if scene exists in build settings
        for (int i = 0; i < SceneManager.sceneCountInBuildSettings; i++)
        {
            string scenePath = SceneUtility.GetScenePathByBuildIndex(i);
            string sceneNameFromPath = System.IO.Path.GetFileNameWithoutExtension(scenePath);
            
            if (sceneNameFromPath == sceneName)
            {
                SceneManager.LoadScene(sceneName);
                return;
            }
        }
        
        // Scene not found, show message
        Debug.LogWarning($"Scene '{sceneName}' not found in build settings. Create the scene first.");
    }

    private void OnContinueClicked()
    {
        Debug.Log("Continue Story clicked - Feature coming soon!");
    }

    private void OnNewGameClicked()
    {
        Debug.Log("New Game clicked - Feature coming soon!");
    }

    private void OnCharacterClicked()
    {
        Debug.Log("Character Customization clicked - Feature coming soon!");
    }

    private void OnGalleryClicked()
    {
        Debug.Log("Gallery clicked - Feature coming soon!");
    }

    private void OnSettingsClicked()
    {
        Debug.Log("Settings clicked - Feature coming soon!");
    }

    private void OnSaveLoadClicked()
    {
        Debug.Log("Save/Load Game clicked - Feature coming soon!");
    }

    private void OnStartButtonClicked()
    {
        Debug.Log("Start Journey clicked!");
        HideWelcomeBox();
        ShowMainMenu();
    }

    private void OnSoundButtonClicked()
    {
        isSoundMuted = !isSoundMuted;
        UpdateSoundButton();
        Debug.Log($"Sound {(isSoundMuted ? "muted" : "unmuted")}");
    }

    private void OnMessageBoxClicked(ClickEvent evt)
    {
        HideMessageBox();
    }

    private void OnWelcomeBoxClicked(ClickEvent evt)
    {
        // Prevent clicking through to background
        evt.StopPropagation();
    }

    private void ShowWelcomeMessage()
    {
        if (messageBox != null)
        {
            messageBox.AddToClassList("active");
            Invoke(nameof(HideMessageBox), 3f); // Auto-hide after 3 seconds
        }
    }

    private void HideMessageBox()
    {
        if (messageBox != null)
        {
            messageBox.RemoveFromClassList("active");
            Invoke(nameof(ShowWelcomeBox), 0.5f); // Show welcome box after message disappears
        }
    }

    private void ShowWelcomeBox()
    {
        if (welcomeBox != null)
        {
            welcomeBox.AddToClassList("active");
        }
    }

    private void HideWelcomeBox()
    {
        if (welcomeBox != null)
        {
            welcomeBox.RemoveFromClassList("active");
        }
    }

    private void ShowMainMenu()
    {
        if (menuContainer != null)
        {
            menuContainer.style.display = DisplayStyle.Flex;
        }
    }

    private void UpdateSoundButton()
    {
        if (soundButton != null)
        {
            if (isSoundMuted)
            {
                soundButton.AddToClassList("muted");
                var soundIcon = soundButton.Q<Label>("sound-icon");
                if (soundIcon != null) soundIcon.text = "🔇";
            }
            else
            {
                soundButton.RemoveFromClassList("muted");
                var soundIcon = soundButton.Q<Label>("sound-icon");
                if (soundIcon != null) soundIcon.text = "🔊";
            }
        }
    }
}

