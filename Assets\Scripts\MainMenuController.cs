using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.SceneManagement;

public class MainMenuController : MonoBehaviour
{
    private UIDocument menuDocument;
    private Button continueBtn;
    private Button newGameBtn;
    private Button characterBtn;
    private Button galleryBtn;
    private Button settingsBtn;
    private Button saveLoadBtn;

    private void OnEnable()
    {
        menuDocument = GetComponent<UIDocument>();
        var root = menuDocument.rootVisualElement;

        // Get button references
        continueBtn = root.Q<Button>("continue-btn");
        newGameBtn = root.Q<Button>("newgame-btn");
        characterBtn = root.Q<Button>("character-btn");
        galleryBtn = root.Q<Button>("gallery-btn");
        settingsBtn = root.Q<Button>("settings-btn");
        saveLoadBtn = root.Q<Button>("saveload-btn");

        // Register button callbacks
        continueBtn.clicked += OnContinueClicked;
        newGameBtn.clicked += OnNewGameClicked;
        characterBtn.clicked += OnCharacterClicked;
        galleryBtn.clicked += OnGalleryClicked;
        settingsBtn.clicked += OnSettingsClicked;
        saveLoadBtn.clicked += OnSaveLoadClicked;

        // Add fade-in effect
        var menuContainer = root.Q("menu-container");
        menuContainer.AddToClassList("fade-in");
    }

    private void OnDisable()
    {
        if (continueBtn != null) continueBtn.clicked -= OnContinueClicked;
        if (newGameBtn != null) newGameBtn.clicked -= OnNewGameClicked;
        if (characterBtn != null) characterBtn.clicked -= OnCharacterClicked;
        if (galleryBtn != null) galleryBtn.clicked -= OnGalleryClicked;
        if (settingsBtn != null) settingsBtn.clicked -= OnSettingsClicked;
        if (saveLoadBtn != null) saveLoadBtn.clicked -= OnSaveLoadClicked;
    }

    private void LoadSceneIfExists(string sceneName)
    {
        // Check if scene exists in build settings
        for (int i = 0; i < SceneManager.sceneCountInBuildSettings; i++)
        {
            string scenePath = SceneUtility.GetScenePathByBuildIndex(i);
            string sceneNameFromPath = System.IO.Path.GetFileNameWithoutExtension(scenePath);
            
            if (sceneNameFromPath == sceneName)
            {
                SceneManager.LoadScene(sceneName);
                return;
            }
        }
        
        // Scene not found, show message
        Debug.LogWarning($"Scene '{sceneName}' not found in build settings. Create the scene first.");
    }

    private void OnContinueClicked()
    {
        Debug.Log("Continue Story clicked - Feature coming soon!");
    }

    private void OnNewGameClicked()
    {
        Debug.Log("New Game clicked - Feature coming soon!");
    }

    private void OnCharacterClicked()
    {
        Debug.Log("Character Customization clicked - Feature coming soon!");
    }

    private void OnGalleryClicked()
    {
        Debug.Log("Gallery clicked - Feature coming soon!");
    }

    private void OnSettingsClicked()
    {
        Debug.Log("Settings clicked - Feature coming soon!");
    }

    private void OnSaveLoadClicked()
    {
        Debug.Log("Save/Load Game clicked - Feature coming soon!");
    }
}

