<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/MainMenu.uss" />

    <!-- Main Menu Container -->
    <ui:VisualElement name="menu-container" class="menu-container">
        <!-- Game Title -->
        <ui:VisualElement name="game-title" class="game-title">
            <ui:Label text="MATE" name="title-line1" class="title-line title-mate" />
            <ui:Label text="THE KING" name="title-line2" class="title-line title-the-king" />
        </ui:VisualElement>

        <!-- Subtitle -->
        <ui:Label text="ADVENTURE" name="subtitle-text" class="subtitle-text" />

        <!-- Play Button -->
        <ui:Button text="PLAY NOW ♔" name="Startgame-btn" class="play-button" />
    </ui:VisualElement>
</ui:UXML>
