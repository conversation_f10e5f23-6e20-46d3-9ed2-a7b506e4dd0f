<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/MainMenu.uss" />

    <!-- Background Effects -->
    <ui:VisualElement name="background-container" class="background-container">
        <ui:VisualElement name="side-lights" class="side-lights" />
        <ui:VisualElement name="corner-lights" class="corner-lights" />
        <ui:VisualElement name="cosmic-background" class="cosmic-background" />
        <ui:VisualElement name="stars" class="stars" />
    </ui:VisualElement>

    <!-- Welcome Message Box -->
    <ui:VisualElement name="message-box" class="message-box">
        <ui:VisualElement name="message-content" class="message-content">
            <ui:VisualElement name="cyber-border" class="cyber-border" />
            <ui:Label text="Welcome Commander!" name="welcome-title" class="welcome-title" />
            <ui:VisualElement name="separator" class="separator">
                <ui:VisualElement name="separator-line" class="separator-line" />
                <ui:VisualElement name="diamond" class="diamond" />
                <ui:VisualElement name="separator-line" class="separator-line" />
            </ui:VisualElement>
            <ui:Label text="Let's GO!" name="welcome-text" class="welcome-text" />
            <ui:Label text="⚡" name="pulse-icon" class="pulse-icon" />
        </ui:VisualElement>
    </ui:VisualElement>

    <!-- Welcome Box -->
    <ui:VisualElement name="welcome-box" class="welcome-box">
        <ui:VisualElement name="welcome-content" class="welcome-content">
            <ui:Label text="Welcome to" name="welcome-subtitle" class="welcome-subtitle" />
            <ui:Label text="Chess King Adventure" name="welcome-main-title" class="welcome-main-title" />
            <ui:Button text="Start Journey" name="start-button" class="start-button" />
        </ui:VisualElement>
    </ui:VisualElement>

    <!-- Main Menu Container -->
    <ui:VisualElement name="menu-container" class="menu-container">
        <!-- Game Title -->
        <ui:VisualElement name="game-title" class="game-title">
            <ui:Label text="Mate" name="title-line1" class="title-line" />
            <ui:Label text="The King" name="title-line2" class="title-line" />
        </ui:VisualElement>

        <!-- Subtitle -->
        <ui:VisualElement name="subtitle" class="subtitle">
            <ui:Label text="Adventure" name="subtitle-text" class="subtitle-text" />
        </ui:VisualElement>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mate The King Adventure - Chess Game</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="side-lights"></div>
    <div class="corner-lights"></div>
    
    <div class="message-box" id="messageBox">
        <div class="message-content">
            <div class="cyber-border"></div>
            <h2>Welcome Commander!</h2>
            <div class="separator">
                <span></span>
                <div class="diamond"></div>
                <span></span>
            </div>
            <p>Let's GO!</p>
            <div class="pulse-icon">⚡</div>
        </div>
    </div>

    <div class="welcome-box" id="welcomeBox">
        <div class="welcome-content">
            <h3>Welcome to</h3>
            <h2>Chess King Adventure</h2>
            <button class="start-button" id="startButton">Start Journey</button>
        </div>
    </div>
    
    <div class="menu-container">
        <h1 class="game-title">
            <span>Mate</span>
            <span>The King</span>
        </h1>
        
        <h2 class="subtitle">
            <span>Adventure</span>
        </h2>
        <div class="menu">
            <button class="menu-button" id="playButton">Play Now</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

    <!-- Chess Piece Decoration -->
    <ui:VisualElement name="chess-piece" class="chess-piece">
        <ui:Label text="♔" name="chess-icon" class="chess-icon" />
    </ui:VisualElement>

    <!-- Sound Control -->
    <ui:Button name="sound-button" class="sound-button top-right">
        <ui:Label text="🔊" name="sound-icon" class="sound-icon" />
    </ui:Button>
</ui:UXML>




